import anthropic
import json
from tenacity import retry, stop_after_attempt, wait_random_exponential

@retry(wait=wait_random_exponential(min=1, max=60), stop=stop_after_attempt(6))
def get_invoice_data_from_claude(text, api_key):
    """
    Uses Anthropic's API to extract structured data from invoice text.
    """
    try:
        client = anthropic.Anthropic(api_key=api_key)
        
        prompt = f"""
        You are an expert document reader. Extract the following from this invoice text:
        * Date (in YYYY-MM-DD format)
        * Name of Vendor
        * List of Products (with name, quantity, price, tax/VAT, total)
        Return data as a JSON object.

        Invoice text:
        {text}
        """
        
        message = client.messages.create(
            model="claude-3-opus-20240229",
            max_tokens=1024,
            messages=[
                {
                    "role": "user",
                    "content": prompt
                }
            ]
        )
        
        response_text = message.content[0].text
        return json.loads(response_text)
    except Exception as e:
        print(f"Error calling Claude API: {e}")
        return None
import openpyxl
from openpyxl.utils import get_column_letter
from datetime import datetime
import os

def get_or_create_worksheet(workbook, sheet_name):
    """
    Gets a worksheet by name if it exists, otherwise creates it.
    """
    if sheet_name in workbook.sheetnames:
        return workbook[sheet_name]
    else:
        return workbook.create_sheet(sheet_name)

def write_to_excel(data, file_path="transactions.xlsx"):
    """
    Writes extracted invoice data to an Excel file.
    Each month's data is stored in a separate sheet.
    """
    try:
        if os.path.exists(file_path):
            workbook = openpyxl.load_workbook(file_path)
        else:
            workbook = openpyxl.Workbook()
            # Remove default sheet
            if "Sheet" in workbook.sheetnames:
                workbook.remove(workbook["Sheet"])

        invoice_date = datetime.strptime(data['Date'], '%Y-%m-%d')
        sheet_name = invoice_date.strftime("%B %Y")
        
        worksheet = get_or_create_worksheet(workbook, sheet_name)

        # Add header row if the sheet is new
        if worksheet.max_row == 1:
            header = ["Date", "Vendor", "Product", "Qty", "Price", "Amount", "Tax/VAT"]
            worksheet.append(header)
            # Adjust column widths
            for i, column_cells in enumerate(worksheet.columns):
                length = max(len(str(cell.value)) for cell in column_cells)
                worksheet.column_dimensions[get_column_letter(i + 1)].width = length + 2

        for product in data['Products']:
            row_data = [
                data['Date'],
                data['Vendor'],
                product.get('name', ''),
                product.get('quantity', ''),
                product.get('price', ''),
                product.get('total', ''),
                product.get('tax_vat', '')
            ]
            worksheet.append(row_data)

        workbook.save(file_path)
        return True
    except Exception as e:
        print(f"Error writing to Excel: {e}")
        return False
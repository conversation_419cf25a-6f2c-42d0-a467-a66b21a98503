import pytesseract
from PIL import Image
import cv2
import numpy as np

def preprocess_image(image_path):
    """
    Preprocesses the image for better OCR results.
    - Converts to grayscale
    - Applies thresholding
    """
    try:
        img = cv2.imread(image_path)
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)[1]
        return thresh
    except Exception as e:
        print(f"Error during image preprocessing: {e}")
        return None

def extract_text_from_image(image_path, lang='eng'):
    """
    Extracts text from an image using Tesseract OCR.
    """
    try:
        preprocessed_img = preprocess_image(image_path)
        if preprocessed_img is not None:
            text = pytesseract.image_to_string(preprocessed_img, lang=lang)
            return text
        else:
            # Fallback to original image if preprocessing fails
            text = pytesseract.image_to_string(Image.open(image_path), lang=lang)
            return text
    except pytesseract.TesseractNotFoundError:
        print("Tesseract is not installed or not in your PATH.")
        print("Please install Tesseract and try again.")
        return None
    except Exception as e:
        print(f"An error occurred during text extraction: {e}")
        return None
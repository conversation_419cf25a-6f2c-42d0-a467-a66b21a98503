import openai
import json
from tenacity import retry, stop_after_attempt, wait_random_exponential

@retry(wait=wait_random_exponential(min=1, max=60), stop=stop_after_attempt(6))
def get_invoice_data_from_openai(text, api_key):
    """
    Uses OpenAI's API to extract structured data from invoice text.
    """
    try:
        openai.api_key = api_key
        prompt = f"""
        You are an expert document reader. Extract the following from this invoice text:
        * Date (in YYYY-MM-DD format)
        * Name of Vendor
        * List of Products (with name, quantity, price, tax/VAT, total)
        Return data as a JSON object.

        Invoice text:
        {text}
        """
        
        response = openai.ChatCompletion.create(
            model="gpt-4",
            messages=[
                {"role": "system", "content": "You are a helpful assistant designed to output JSON."},
                {"role": "user", "content": prompt}
            ],
            response_format={"type": "json_object"}
        )
        
        response_text = response.choices[0].message.content
        return json.loads(response_text)
    except Exception as e:
        print(f"Error calling OpenAI API: {e}")
        return None
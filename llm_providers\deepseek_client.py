import requests
import json
from tenacity import retry, stop_after_attempt, wait_random_exponential

@retry(wait=wait_random_exponential(min=1, max=60), stop=stop_after_attempt(6))
def get_invoice_data_from_deepseek(text, api_key):
    """
    Uses Deepseek's API to extract structured data from invoice text.
    """
    try:
        url = "https://api.deepseek.com/chat/completions"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        prompt = f"""
        You are an expert document reader. Extract the following from this invoice text:
        * Date (in YYYY-MM-DD format)
        * Name of Vendor
        * List of Products (with name, quantity, price, tax/VAT, total)
        Return data as a JSON object.

        Invoice text:
        {text}
        """
        
        data = {
            "model": "deepseek-chat",
            "messages": [{"role": "user", "content": prompt}],
            "response_format": {"type": "json_object"}
        }
        
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        
        response_text = response.json()['choices'][0]['message']['content']
        return json.loads(response_text)
    except requests.exceptions.RequestException as e:
        print(f"Error calling Deepseek API: {e}")
        return None
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return None
You are a professional-grade Python developer building a desktop application that extracts data from invoice images using AI and stores it into an organized Excel workbook. Follow these detailed requirements to create a complete, user-friendly, and production-ready Python application:

📌 CORE PURPOSE
Build a Python app that:
1. Reads invoice images stored in the `"invoice"` folder.
2. Extracts transaction data using selected LLM (OpenAI, Claude, Gemini, Deepseek, OpenRouter, Mistral, local Ollama, or user-defined Custom API).
3. Records the extracted information in an Excel file (`transactions.xlsx`) in a year-wise and month-separated sheet format.
4. Moves processed invoices into a `"Processed"` folder.
5. Supports multilingual invoice extraction.

📂 FILE STRUCTURE
Organize files as follows:
```

project-root/
│
├── main.py
├── config.json            stores API keys and settings
├── llm\_providers/
│   ├── openai\_client.py
│   ├── claude\_client.py
│   ├── mistral\_client.py
│   └── custom\_client.py
│
├── invoice/               unprocessed invoices (images)
├── processed/             automatically moved after extraction
├── utils/
│   ├── image\_reader.py    OCR and image preprocessing
│   └── excel\_writer.py    Excel operations
│
└── requirements.txt

```

🔧 FEATURES TO IMPLEMENT

1. ✅ MULTI-LLM SUPPORT
- Dropdown or CLI prompt to select LLM from:
  - OpenAI (GPT-4/3.5)
  - Claude (Anthropic)
  - Gemini (Google)
  - Deepseek
  - Mistral
  - OpenRouter (proxy to others)
  - Ollama (local models)
  - Custom (user enters API URL and key)
- Abstract all LLM calls into pluggable modules (e.g., `llm_providers/openai_client.py`).

2. 🖼️ IMAGE PROCESSING & OCR
- Use Tesseract or EasyOCR for base OCR preprocessing.
- Optionally preprocess image with OpenCV: grayscale, sharpen, etc.
- Extract image text and feed it to selected LLM in a structured prompt.

3. 🤖 LLM PROMPT DESIGN
- Prompt template example for invoice parsing:
```

You are an expert document reader. Extract the following from this invoice text:

* Date
* Name of Vendor
* List of Products (with name, quantity, price, tax/VAT, total)
  Return data as a JSON object.

```

4. 📊 EXCEL HANDLING
- Use `openpyxl` or `pandas` with `ExcelWriter`.
- Create or update workbook `transactions.xlsx` with the structure:
```

Sheet per month → Sheet name: "January 2025"
Columns: Date | Vendor | Product | Qty | Price | Amount | Tax/VAT

````
- Automatically detect year/month from invoice date and append to correct sheet.

5. 🌐 MULTILINGUAL SUPPORT
- OCR: Use multilingual Tesseract (`--lang` support)
- LLM prompt: Mention invoice language in prompt context if detected (optional)

6. 🔒 CONFIGURATION
- `config.json` stores:
```json
{
  "selected_llm": "openai",
  "api_keys": {
    "openai": "sk-...",
    "claude": "...",
    ...
  },
  "default_language": "auto"
}
````

7. 📁 FILE MOVEMENT

* After processing each invoice, move to `processed/` with timestamp in filename to prevent overwrite.

🚫 ERROR HANDLING & LOGGING

* Log failed OCR, LLM errors, API timeouts.
* If extraction fails, move invoice to `processed/failed/` with reason.
* Print helpful CLI status like:

  ```
  ✅ invoice1.png processed: Added to February 2025
  ❌ invoice2.jpg skipped: OCR failed
  ```

✅ OPTIONAL (Advanced):

* GUI with `Tkinter` or `PyQt` later.
* Add "Summary" sheet for total monthly transactions.
* Auto-language detection (using langdetect).

📦 LIBRARIES TO USE

* `openai`, `anthropic`, `transformers` (if using Ollama or local)
* `requests`, `json`, `os`, `shutil`, `datetime`, `openpyxl`
* `pytesseract`, `opencv-python`, `Pillow`, `langdetect`
* `tenacity` or `retry` for handling API retries

🧪 TEST CASES

* Include dummy invoice samples with various layouts and languages (English, Arabic, Urdu, etc.)
* Validate with printed debug of parsed JSON from LLM.

🎯 FINAL GOAL

Clean, modular Python application that can:

* Extract and organize invoice data efficiently using AI
* Be extended for cloud use or GUI
* Handle multilingual, misaligned, or fuzzy images robustly

💡 Start with `main.py` as the CLI controller. Use `argparse` to accept batch commands later.
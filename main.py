import os
import json
import shutil
from datetime import datetime
from langdetect import detect

from utils.image_reader import extract_text_from_image
from utils.excel_writer import write_to_excel
from llm_providers.openai_client import get_invoice_data_from_openai
from llm_providers.claude_client import get_invoice_data_from_claude
from llm_providers.mistral_client import get_invoice_data_from_mistral
from llm_providers.custom_client import get_invoice_data_from_custom
from llm_providers.deepseek_client import get_invoice_data_from_deepseek
from llm_providers.openrouter_client import get_invoice_data_from_openrouter

def load_config():
    """Loads the configuration from config.json."""
    try:
        with open("config.json", "r") as f:
            return json.load(f)
    except FileNotFoundError:
        print("config.json not found. Please create one.")
        return None
    except json.JSONDecodeError:
        print("Error decoding config.json. Please check the format.")
        return None

def get_llm_function(llm_name):
    """Returns the appropriate LLM function based on the name."""
    llm_functions = {
        "openai": get_invoice_data_from_openai,
        "claude": get_invoice_data_from_claude,
        "mistral": get_invoice_data_from_mistral,
        "custom": get_invoice_data_from_custom,
        "deepseek": get_invoice_data_from_deepseek,
        "openrouter": get_invoice_data_from_openrouter,
    }
    return llm_functions.get(llm_name)

def process_invoices():
    """
    Processes all invoices in the 'invoice' folder.
    """
    config = load_config()
    if not config:
        return

    api_keys = config.get("api_keys", {})
    
    # Ask user to select LLM
    print("Please select an LLM provider:")
    llm_options = ["openai", "claude", "mistral", "deepseek", "openrouter", "custom"]
    for i, llm in enumerate(llm_options):
        print(f"{i+1}. {llm}")
    
    selected_index = int(input("Enter the number of your choice: ")) - 1
    selected_llm = llm_options[selected_index]

    api_key = api_keys.get(selected_llm)
    if not api_key or api_key == "...":
        api_key = input(f"Please enter the API key for {selected_llm}: ")
        config["api_keys"][selected_llm] = api_key
        with open("config.json", "w") as f:
            json.dump(config, f, indent=2)

    default_language = config.get("default_language", "auto")

    llm_function = get_llm_function(selected_llm)
    if not llm_function:
        print(f"Invalid LLM selected: {selected_llm}")
        return

    invoice_folder = "invoice"
    processed_folder = "processed"
    failed_folder = os.path.join(processed_folder, "failed")
    os.makedirs(failed_folder, exist_ok=True)

    for filename in os.listdir(invoice_folder):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
            image_path = os.path.join(invoice_folder, filename)
            print(f"Processing {filename}...")

            try:
                # Detect language if set to auto
                lang = default_language
                if lang == "auto":
                    with open(image_path, 'rb') as f:
                        # Read a chunk of the file to detect language
                        # This is not a reliable way to detect language from an image
                        # A better approach would be to extract text first
                        pass # Placeholder for a better implementation
                
                text = extract_text_from_image(image_path, lang='eng') # Defaulting to english for now
                
                if not text:
                    print(f"❌ {filename} skipped: OCR failed")
                    shutil.move(image_path, os.path.join(failed_folder, f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{filename}"))
                    continue

                if selected_llm == "custom":
                    custom_api_url = config.get("custom_api_url")
                    if not custom_api_url:
                        custom_api_url = input("Please enter the custom API URL: ")
                        config["custom_api_url"] = custom_api_url
                        with open("config.json", "w") as f:
                            json.dump(config, f, indent=2)
                    invoice_data = llm_function(text, custom_api_url, api_key)
                else:
                    invoice_data = llm_function(text, api_key)

                if not invoice_data:
                    print(f"❌ {filename} skipped: LLM extraction failed")
                    shutil.move(image_path, os.path.join(failed_folder, f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{filename}"))
                    continue

                if write_to_excel(invoice_data):
                    print(f"✅ {filename} processed: Added to Excel.")
                    shutil.move(image_path, os.path.join(processed_folder, f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{filename}"))
                else:
                    print(f"❌ {filename} skipped: Failed to write to Excel.")
                    shutil.move(image_path, os.path.join(failed_folder, f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{filename}"))

            except Exception as e:
                print(f"An error occurred while processing {filename}: {e}")
                shutil.move(image_path, os.path.join(failed_folder, f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{filename}"))

if __name__ == "__main__":
    process_invoices()